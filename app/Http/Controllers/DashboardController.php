<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\CommandePrincipale;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class DashboardController extends Controller
{
    /**
     * Affiche le dashboard principal du client
     */
    public function index(): Response
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();
        if (!$client) {
            return Inertia::render('auth/complete-profile');
        }

        // Récupérer les statistiques du client
        $stats = $this->getClientStats($client->id);

        // Récupérer les commandes récentes
        $recentOrders = $this->getRecentOrders($client->id);

        // Récupérer les commandes en cours
        $activeOrders = $this->getActiveOrders($client->id);

        return Inertia::render('Dashboard/Index', [
            'client' => $client,
            'stats' => $stats,
            'recentOrders' => $recentOrders,
            'activeOrders' => $activeOrders,
        ]);
    }

    /**
     * Affiche la liste complète des commandes
     */
    public function orders(Request $request): Response
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client) {
            return Inertia::render('Dashboard/Orders', [
                'orders' => [
                    'data' => [],
                    'links' => [],
                    'meta' => ['last_page' => 1]
                ],
                'filters' => $request->only(['status', 'search']),
            ]);
        }

        $query = CommandePrincipale::with([
            'sousCommandes.marchand',
            'sousCommandes.articles.produit',
            'adresseLivraison'
        ])
        ->where('client_id', $client->id)
        ->orderBy('created_at', 'desc');

        // Filtres
        if ($request->has('status') && $request->status !== 'all') {
            $query->where('statut_global', $request->status);
        }

        if ($request->has('search') && !empty($request->search)) {
            $query->where('numero_commande', 'like', '%' . $request->search . '%');
        }

        $orders = $query->paginate(10)->withQueryString();

        return Inertia::render('Dashboard/Orders', [
            'orders' => $orders,
            'filters' => $request->only(['status', 'search']),
        ]);
    }

    /**
     * Affiche les détails d'une commande
     */
    public function orderDetails(string $numeroCommande): Response
    {
        $user = Auth::user();
        $client = Client::where('user_id', $user->id)->first();

        if (!$client) {
            abort(404, 'Client non trouvé');
        }

        $order = CommandePrincipale::with([
            'sousCommandes.marchand',
            'sousCommandes.articles.produit',
            'adresseLivraison',
            'adresseFacturation'
        ])
        ->where('numero_commande', $numeroCommande)
        ->where('client_id', $client->id)
        ->firstOrFail();

        // Ajouter les URLs d'images pour chaque produit
        foreach ($order->sousCommandes as $sousCommande) {
            foreach ($sousCommande->articles as $article) {
                if ($article->produit) {
                    $article->produit->append(['image_urls']);
                }
            }
        }

        return Inertia::render('Dashboard/OrderDetails', [
            'order' => $order,
        ]);
    }

    /**
     * Affiche le profil du client
     */
    public function profile(): Response
    {
        $user = Auth::user();
        $client = Client::with(['adresses'])->where('user_id', $user->id)->first();
        // dd($client, $user->created_at);
        return Inertia::render('Dashboard/Profile', [
            'client' => $client,
            'user' => $user,
        ]);
    }

    /**
     * Récupère les statistiques du client
     */
    private function getClientStats(int $clientId): array
    {
        $totalOrders = CommandePrincipale::where('client_id', $clientId)->count();

        $totalSpent = CommandePrincipale::where('client_id', $clientId)
            ->where('statut_global', '!=', 'Annule')
            ->sum('montant_total_ttc');

        $pendingOrders = CommandePrincipale::where('client_id', $clientId)
            ->whereIn('statut_global', ['EnAttente', 'EnCoursDeTraitement'])
            ->count();

        $deliveredOrders = CommandePrincipale::where('client_id', $clientId)
            ->where('statut_global', 'Livre')
            ->count();

        return [
            'total_orders' => $totalOrders,
            'total_spent' => $totalSpent,
            'pending_orders' => $pendingOrders,
            'delivered_orders' => $deliveredOrders,
        ];
    }

    /**
     * Récupère les commandes récentes
     */
    private function getRecentOrders(int $clientId): array
    {
        return CommandePrincipale::with([
            'sousCommandes.marchand',
            'sousCommandes.articles.produit'
        ])
        ->where('client_id', $clientId)
        ->orderBy('created_at', 'desc')
        ->limit(5)
        ->get()
        ->toArray();
    }

    /**
     * Récupère les commandes en cours
     */
    private function getActiveOrders(int $clientId): array
    {
        return CommandePrincipale::with([
            'sousCommandes.marchand',
            'sousCommandes.articles.produit'
        ])
        ->where('client_id', $clientId)
        ->whereIn('statut_global', ['EnAttente', 'EnCoursDeTraitement', 'Expedie'])
        ->orderBy('created_at', 'desc')
        ->get()
        ->toArray();
    }

    /**
     * Traite la soumission du formulaire de complétion de profil
     */
    public function completeProfile(Request $request): RedirectResponse
    {
        $user = Auth::user();

        // Validation des données
        $validated = $request->validate([
            'prenom' => 'required|string|max:100',
            'nom' => 'required|string|max:100',
            'telephone' => 'nullable|string|max:20',
            'dateDeNaissance' => 'nullable|date',
            'rue' => 'required|string|max:255',
            'ville' => 'required|string|max:100',
            'etat' => 'nullable|string|max:100',
            'pays' => 'required|string|max:100',
            'codePostal' => 'required|string|max:20',
        ]);

        // Créer le profil client
        $client = Client::create([
            'user_id' => $user->id,
            'prenom' => $validated['prenom'],
            'nom' => $validated['nom'],
            'telephone' => $validated['telephone'],
            'dateDeNaissance' => $validated['dateDeNaissance'],
        ]);

        // Créer l'adresse de livraison via l'utilisateur
        $user->adresses()->create([
            'rue' => $validated['rue'],
            'ville' => $validated['ville'],
            'etat' => $validated['etat'],
            'pays' => $validated['pays'],
            'codePostal' => $validated['codePostal'],
            'type' => 'livraison',
            'est_defaut' => true,
        ]);

        return redirect()->route('dashboard')->with('success', 'Votre profil a été complété avec succès !');
    }
}
