<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\StockManagementService;
use App\Services\NotificationService;
use App\Services\CheckoutService;
use App\Services\CommissionCalculationService;
use App\Services\PaymentSplittingService;
use App\Services\EscrowService;
use App\Services\DisputeService;
use App\Models\Produit;
use App\Models\Client;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

/**
 * Contrôleur de test pour valider les services business
 */
class TestController extends Controller
{
    protected StockManagementService $stockService;
    protected NotificationService $notificationService;
    protected CheckoutService $checkoutService;
    protected CommissionCalculationService $commissionService;
    protected PaymentSplittingService $paymentSplittingService;
    protected EscrowService $escrowService;
    protected DisputeService $disputeService;

    public function __construct(
        StockManagementService $stockService,
        NotificationService $notificationService,
        CheckoutService $checkoutService,
        CommissionCalculationService $commissionService,
        PaymentSplittingService $paymentSplittingService,
        EscrowService $escrowService,
        DisputeService $disputeService
    ) {
        $this->stockService = $stockService;
        $this->notificationService = $notificationService;
        $this->checkoutService = $checkoutService;
        $this->commissionService = $commissionService;
        $this->paymentSplittingService = $paymentSplittingService;
        $this->escrowService = $escrowService;
        $this->disputeService = $disputeService;
    }

    /**
     * Test de vérification des stocks
     */
    public function testStockCheck(Request $request): JsonResponse
    {
        try {
            // Récupérer quelques produits pour le test
            $produits = Produit::where('statut', 'actif')
                ->where('stock', '>', 0)
                ->take(3)
                ->get();

            if ($produits->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Aucun produit disponible pour le test'
                ]);
            }

            // Créer un panier de test
            $cartData = $produits->map(function ($produit) {
                return [
                    'id' => $produit->id,
                    'quantity' => min(2, $produit->stock) // Prendre max 2 ou le stock disponible
                ];
            })->toArray();

            // Tester la vérification des stocks
            $stockCheck = $this->stockService->checkStockAvailability($cartData);

            return response()->json([
                'success' => true,
                'message' => 'Test de vérification des stocks réussi',
                'data' => [
                    'cart_tested' => $cartData,
                    'stock_check_result' => $stockCheck
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur test stock check', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors du test de vérification des stocks',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test de création de commande complète
     */
    public function testOrderCreation(Request $request): JsonResponse
    {
        try {
            // Récupérer un client de test
            $client = Client::first();
            if (!$client) {
                return response()->json([
                    'success' => false,
                    'message' => 'Aucun client trouvé pour le test'
                ]);
            }

            // Récupérer des produits de différents marchands
            $produits = Produit::where('statut', 'actif')
                ->where('stock', '>', 0)
                ->with('marchand')
                ->take(3)
                ->get();

            if ($produits->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Aucun produit disponible pour le test'
                ]);
            }

            // Créer un panier de test
            $cartData = $produits->map(function ($produit) {
                return [
                    'id' => $produit->id,
                    'quantity' => 1,
                    'price' => $produit->prix
                ];
            })->toArray();

            // Récupérer une adresse du client
            $adresse = $client->user->adresses()->first();
            if (!$adresse) {
                return response()->json([
                    'success' => false,
                    'message' => 'Aucune adresse trouvée pour le client de test'
                ]);
            }

            // Tester la création de commande
            $orderResult = $this->checkoutService->createOrderFromCart(
                $cartData,
                $client->id,
                $adresse->id,
                $adresse->id,
                ['test' => true]
            );

            return response()->json([
                'success' => true,
                'message' => 'Test de création de commande réussi',
                'data' => [
                    'cart_tested' => $cartData,
                    'client_id' => $client->id,
                    'order_result' => [
                        'commande_principale_id' => $orderResult['commande_principale']->id,
                        'numero_commande' => $orderResult['commande_principale']->numero_commande,
                        'nombre_sous_commandes' => count($orderResult['sous_commandes']),
                        'montant_total' => $orderResult['totals']['montant_total_ttc']
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur test order creation', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors du test de création de commande',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test d'annulation de commande et libération des stocks
     */
    public function testOrderCancellation(Request $request): JsonResponse
    {
        try {
            $commandeId = $request->input('commande_id');

            if (!$commandeId) {
                return response()->json([
                    'success' => false,
                    'message' => 'ID de commande requis'
                ], 400);
            }

            // Tester l'annulation
            $cancelResult = $this->checkoutService->cancelOrder($commandeId, 'Test d\'annulation');

            return response()->json([
                'success' => true,
                'message' => 'Test d\'annulation de commande réussi',
                'data' => $cancelResult
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur test order cancellation', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors du test d\'annulation de commande',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test complet du workflow
     */
    public function testCompleteWorkflow(Request $request): JsonResponse
    {
        try {
            $results = [];

            // 1. Test de vérification des stocks
            $stockTestResponse = $this->testStockCheck($request);
            $stockTestData = json_decode($stockTestResponse->getContent(), true);
            $results['stock_check'] = $stockTestData;

            if (!$stockTestData['success']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Échec du test de vérification des stocks',
                    'results' => $results
                ]);
            }

            // 2. Test de création de commande
            $orderTestResponse = $this->testOrderCreation($request);
            $orderTestData = json_decode($orderTestResponse->getContent(), true);
            $results['order_creation'] = $orderTestData;

            if (!$orderTestData['success']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Échec du test de création de commande',
                    'results' => $results
                ]);
            }

            // 3. Test d'annulation (optionnel)
            if ($request->input('test_cancellation', false)) {
                $commandeId = $orderTestData['data']['order_result']['commande_principale_id'];
                $request->merge(['commande_id' => $commandeId]);

                $cancelTestResponse = $this->testOrderCancellation($request);
                $cancelTestData = json_decode($cancelTestResponse->getContent(), true);
                $results['order_cancellation'] = $cancelTestData;
            }

            return response()->json([
                'success' => true,
                'message' => 'Test complet du workflow réussi',
                'results' => $results
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur test complete workflow', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors du test complet du workflow',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test de calcul des commissions
     */
    public function testCommissionCalculation(Request $request): JsonResponse
    {
        try {
            // Récupérer une commande principale avec sous-commandes
            $commandePrincipale = \App\Models\CommandePrincipale::with('sousCommandes.marchand.abonnementActuel')
                ->whereHas('sousCommandes')
                ->first();

            if (!$commandePrincipale) {
                return response()->json([
                    'success' => false,
                    'message' => 'Aucune commande avec sous-commandes trouvée pour le test'
                ]);
            }

            // Calculer les commissions pour toutes les sous-commandes
            $commissionsResult = $this->commissionService->calculateCommissionsForOrder(
                $commandePrincipale->sousCommandes
            );

            return response()->json([
                'success' => true,
                'message' => 'Test de calcul des commissions réussi',
                'data' => [
                    'commande_principale_id' => $commandePrincipale->id,
                    'numero_commande' => $commandePrincipale->numero_commande,
                    'commissions_result' => $commissionsResult
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur test commission calculation', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors du test de calcul des commissions',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test de fractionnement de paiement
     */
    public function testPaymentSplitting(Request $request): JsonResponse
    {
        try {
            // Récupérer une commande principale avec paiement confirmé
            $commandePrincipale = \App\Models\CommandePrincipale::with('sousCommandes.marchand')
                ->where('statut_paiement', 'Complété')
                ->whereHas('sousCommandes')
                ->first();

            if (!$commandePrincipale) {
                return response()->json([
                    'success' => false,
                    'message' => 'Aucune commande payée trouvée pour le test'
                ]);
            }

            // Simuler des données de paiement
            $paymentData = [
                'method' => 'paypal',
                'transaction_id' => 'TEST_' . time(),
                'status' => 'completed'
            ];

            // Tester le fractionnement
            $splittingResult = $this->paymentSplittingService->processPaymentSplitting(
                $commandePrincipale,
                $paymentData
            );

            return response()->json([
                'success' => true,
                'message' => 'Test de fractionnement de paiement réussi',
                'data' => [
                    'commande_principale_id' => $commandePrincipale->id,
                    'payment_data' => $paymentData,
                    'splitting_result' => $splittingResult
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur test payment splitting', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors du test de fractionnement de paiement',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test de mise en escrow
     */
    public function testEscrowHold(Request $request): JsonResponse
    {
        try {
            // Récupérer une commande principale récente
            $commande = \App\Models\CommandePrincipale::with('sousCommandes')
                ->where('statut_paiement', 'Complété')
                ->whereDoesntHave('escrowTransactions')
                ->first();

            if (!$commande) {
                return response()->json([
                    'success' => false,
                    'message' => 'Aucune commande éligible pour test escrow trouvée'
                ]);
            }

            // Simuler des données de paiement
            $paymentData = [
                'method' => 'paypal',
                'transaction_id' => 'TEST_ESCROW_' . time(),
                'status' => 'completed',
                'amount' => $commande->montant_total_ttc,
                'currency' => 'FCFA'
            ];

            // Tester la mise en escrow
            $escrowResult = $this->escrowService->holdFundsInEscrow($commande, $paymentData);

            return response()->json([
                'success' => true,
                'message' => 'Test de mise en escrow réussi',
                'data' => [
                    'commande_principale_id' => $commande->id,
                    'numero_commande' => $commande->numero_commande,
                    'escrow_result' => $escrowResult
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur test escrow hold', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors du test de mise en escrow',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test de libération escrow
     */
    public function testEscrowRelease(Request $request): JsonResponse
    {
        try {
            // Récupérer une transaction escrow en statut 'held'
            $escrowTransaction = \App\Models\EscrowTransaction::where('statut', 'held')
                ->with('commandePrincipale.sousCommandes')
                ->first();

            if (!$escrowTransaction) {
                return response()->json([
                    'success' => false,
                    'message' => 'Aucune transaction escrow en attente trouvée'
                ]);
            }

            // Marquer les sous-commandes comme livrées pour le test
            $escrowTransaction->commandePrincipale->sousCommandes()->update([
                'statut' => 'livre',
                'date_livraison_reelle' => now()->subHours(49) // 49h pour dépasser le délai de sécurité
            ]);

            $escrowTransaction->commandePrincipale->update([
                'statut_global' => 'TotalementLivré',
                'date_livraison_complete' => now()->subHours(49)
            ]);

            // Tester la libération
            $releaseResult = $this->escrowService->releaseFundsToMerchants($escrowTransaction->commandePrincipale);

            return response()->json([
                'success' => true,
                'message' => 'Test de libération escrow réussi',
                'data' => [
                    'escrow_transaction_id' => $escrowTransaction->id,
                    'commande_principale_id' => $escrowTransaction->commande_principale_id,
                    'release_result' => $releaseResult
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur test escrow release', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors du test de libération escrow',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test de remboursement escrow
     */
    public function testEscrowRefund(Request $request): JsonResponse
    {
        try {
            // Récupérer une transaction escrow en statut 'held' ou 'disputed'
            $escrowTransaction = \App\Models\EscrowTransaction::whereIn('statut', ['held', 'disputed'])
                ->with('commandePrincipale')
                ->first();

            if (!$escrowTransaction) {
                return response()->json([
                    'success' => false,
                    'message' => 'Aucune transaction escrow éligible pour remboursement trouvée'
                ]);
            }

            // Tester le remboursement
            $refundResult = $this->escrowService->refundFundsToClient(
                $escrowTransaction->commandePrincipale,
                'Test de remboursement automatique'
            );

            return response()->json([
                'success' => true,
                'message' => 'Test de remboursement escrow réussi',
                'data' => [
                    'escrow_transaction_id' => $escrowTransaction->id,
                    'commande_principale_id' => $escrowTransaction->commande_principale_id,
                    'refund_result' => $refundResult
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur test escrow refund', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors du test de remboursement escrow',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Statistiques escrow
     */
    public function testEscrowStats(Request $request): JsonResponse
    {
        try {
            $statsResult = $this->escrowService->getEscrowStats();

            return response()->json([
                'success' => true,
                'message' => 'Statistiques escrow récupérées',
                'data' => $statsResult
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur test escrow stats', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des statistiques escrow',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test de création de litige
     */
    public function testDisputeCreation(Request $request): JsonResponse
    {
        try {
            // Récupérer une commande principale récente
            $commande = \App\Models\CommandePrincipale::with(['client', 'sousCommandes'])
                ->where('statut_paiement', 'Complété')
                ->first();

            if (!$commande) {
                return response()->json([
                    'success' => false,
                    'message' => 'Aucune commande éligible pour test de litige trouvée'
                ]);
            }

            // Données de test pour le litige
            $disputeData = [
                'commande_principale_id' => $commande->id,
                'type_litige' => 'produit_defectueux',
                'sujet' => 'Test - Produit défectueux',
                'description' => 'Test de création de litige automatique - Produit reçu endommagé',
                'solution_souhaitee' => 'Remboursement complet',
                'montant_conteste' => $commande->montant_total_ttc,
                'pieces_jointes' => [],
                'source' => 'api_test'
            ];

            // Créer le litige
            $result = $this->disputeService->creerLitige($disputeData);

            return response()->json([
                'success' => true,
                'message' => 'Test de création de litige réussi',
                'data' => [
                    'commande_principale_id' => $commande->id,
                    'numero_commande' => $commande->numero_commande,
                    'dispute_result' => $result
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur test création litige', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors du test de création de litige',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test d'ajout de message à un litige
     */
    public function testDisputeMessage(Request $request): JsonResponse
    {
        try {
            // Récupérer un litige ouvert
            $dispute = \App\Models\Dispute::where('statut', 'ouvert')->first();

            if (!$dispute) {
                return response()->json([
                    'success' => false,
                    'message' => 'Aucun litige ouvert trouvé pour test de message'
                ]);
            }

            // Données de test pour le message
            $messageData = [
                'auteur_type' => 'admin',
                'auteur_id' => 1, // ID admin de test
                'auteur_nom' => 'Admin Test',
                'message' => 'Test de message automatique - Nous avons bien reçu votre litige et allons le traiter rapidement.',
                'type_message' => 'message',
                'pieces_jointes' => [],
                'interne' => false,
                'metadata' => ['test' => true]
            ];

            // Ajouter le message
            $result = $this->disputeService->ajouterMessage($dispute->id, $messageData);

            return response()->json([
                'success' => true,
                'message' => 'Test d\'ajout de message réussi',
                'data' => [
                    'dispute_id' => $dispute->id,
                    'numero_litige' => $dispute->numero_litige,
                    'message_result' => $result
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur test message litige', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors du test d\'ajout de message',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test de changement de statut de litige
     */
    public function testDisputeStatusChange(Request $request): JsonResponse
    {
        try {
            // Récupérer un litige ouvert
            $dispute = \App\Models\Dispute::where('statut', 'ouvert')->first();

            if (!$dispute) {
                return response()->json([
                    'success' => false,
                    'message' => 'Aucun litige ouvert trouvé pour test de changement de statut'
                ]);
            }

            // Changer le statut vers 'en_cours'
            $result = $this->disputeService->changerStatut(
                $dispute->id,
                'en_cours',
                1, // ID admin de test
                'Test de changement de statut automatique'
            );

            return response()->json([
                'success' => true,
                'message' => 'Test de changement de statut réussi',
                'data' => [
                    'dispute_id' => $dispute->id,
                    'numero_litige' => $dispute->numero_litige,
                    'status_change_result' => $result
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur test changement statut litige', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors du test de changement de statut',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Statistiques des litiges
     */
    public function testDisputeStats(Request $request): JsonResponse
    {
        try {
            $statsResult = $this->disputeService->getStatistiques();

            return response()->json([
                'success' => true,
                'message' => 'Statistiques des litiges récupérées',
                'data' => $statsResult
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur test stats litiges', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des statistiques',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Informations sur l'état des services
     */
    public function serviceStatus(): JsonResponse
    {
        try {
            return response()->json([
                'success' => true,
                'message' => 'Services business opérationnels',
                'services' => [
                    'StockManagementService' => [
                        'status' => 'active',
                        'methods' => [
                            'checkStockAvailability',
                            'reserveStock',
                            'releaseStock'
                        ]
                    ],
                    'NotificationService' => [
                        'status' => 'active',
                        'methods' => [
                            'notifyOrderCreated',
                            'notifyPaymentConfirmed',
                            'notifyOrderShipped',
                            'notifyMerchantsNewOrder'
                        ]
                    ],
                    'CheckoutService' => [
                        'status' => 'active',
                        'methods' => [
                            'createOrderFromCart',
                            'confirmPayment',
                            'cancelOrder'
                        ]
                    ],
                    'CommissionCalculationService' => [
                        'status' => 'active',
                        'methods' => [
                            'calculateCommissionForSubOrder',
                            'calculateCommissionsForOrder',
                            'calculateCommissionsForPeriod'
                        ]
                    ],
                    'PaymentSplittingService' => [
                        'status' => 'active',
                        'methods' => [
                            'processPaymentSplitting',
                            'processScheduledPayouts'
                        ]
                    ],
                    'EscrowService' => [
                        'status' => 'active',
                        'methods' => [
                            'holdFundsInEscrow',
                            'releaseFundsToMerchants',
                            'refundFundsToClient',
                            'handlePartialDispute',
                            'processEligibleReleases',
                            'getEscrowStats'
                        ]
                    ],
                    'DisputeService' => [
                        'status' => 'active',
                        'methods' => [
                            'creerLitige',
                            'ajouterMessage',
                            'changerStatut',
                            'resoudreAvecRemboursement',
                            'traiterEscaladeAutomatique',
                            'getStatistiques'
                        ]
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la vérification du statut des services',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
