<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SizeGuide;
use App\Models\Categorie;
use App\Models\Produit;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class SizeGuideController extends Controller
{
    /**
     * Récupère tous les guides des tailles actifs
     *
     * @return JsonResponse
     */
    public function getAllGuides(): JsonResponse
    {
        try {
            // Récupérer tous les guides de tailles actifs
            $sizeGuides = SizeGuide::where('is_active', true)->get();

            return response()->json([
                'size_guides' => $sizeGuides->map(function ($guide) {
                    return [
                        'id' => $guide->id,
                        'name' => $guide->getLocalizedNameAttribute(),
                        'name_fr' => $guide->name_fr,
                        'name_en' => $guide->name_en,
                        'category' => $guide->category,
                        'measurement_types' => $guide->measurement_types,
                        'size_systems' => $guide->size_systems,
                        'size_chart' => $guide->size_chart,
                        'instructions' => $guide->getLocalizedInstructionsAttribute(),
                        'instructions_fr' => $guide->instructions_fr,
                        'instructions_en' => $guide->instructions_en,
                        'fitting_tips' => $guide->fitting_tips,
                        'image' => $guide->image ? url('/storage/' . $guide->image) : null,
                    ];
                }),
            ]);
        } catch (\Exception $e) {
            Log::error('Erreur dans SizeGuideController::getAllGuides: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Une erreur est survenue lors de la récupération des guides de tailles',
                'message' => $e->getMessage(),
            ], 500);
        }
    }
    /**
     * Récupère le guide des tailles pour une catégorie spécifique
     *
     * @param Request $request
     * @param string $categoryId
     * @return JsonResponse
     */
    public function getForCategory(Request $request, string $categoryId): JsonResponse
    {
        $category = Categorie::find($categoryId);

        if (!$category) {
            return response()->json(['error' => 'Catégorie non trouvée'], 404);
        }

        // Récupérer les guides de tailles associés à cette catégorie
        $sizeGuides = $category->sizeGuides()->where('is_active', true)->get();

        if ($sizeGuides->isEmpty()) {
            // Si aucun guide n'est associé directement, chercher dans les catégories parentes
            $parentCategory = $category->categorieParent;
            while ($parentCategory && $sizeGuides->isEmpty()) {
                $sizeGuides = $parentCategory->sizeGuides()->where('is_active', true)->get();
                $parentCategory = $parentCategory->categorieParent;
            }

            // Si toujours aucun guide, chercher par type de catégorie
            if ($sizeGuides->isEmpty()) {
                // Déterminer le type de catégorie (vêtements, chaussures, etc.)
                $categoryType = $this->determineCategoryType($category);

                if ($categoryType) {
                    $sizeGuides = SizeGuide::where('category', $categoryType)
                        ->where('is_active', true)
                        ->get();
                }
            }
        }

        return response()->json([
            'category' => [
                'id' => $category->id,
                'name' => $category->getTranslatedName(),
            ],
            'size_guides' => $sizeGuides->map(function ($guide) {
                return [
                    'id' => $guide->id,
                    'name' => $guide->getLocalizedNameAttribute(),
                    'category' => $guide->category,
                    'measurement_types' => $guide->measurement_types,
                    'size_systems' => $guide->size_systems,
                    'size_chart' => $guide->size_chart,
                    'instructions' => $guide->getLocalizedInstructionsAttribute(),
                    'fitting_tips' => $guide->fitting_tips,
                    'image' => $guide->image ? url('/storage/' . $guide->image) : null,
                ];
            }),
        ]);
    }

    /**
     * Récupère le guide des tailles pour un produit spécifique
     *
     * @param Request $request
     * @param string $productId
     * @return JsonResponse
     */
    public function getForProduct(Request $request, string $productId): JsonResponse
    {
        try {
            $product = Produit::with('variants')->find($productId);

            if (!$product) {
                return response()->json(['error' => 'Produit non trouvé'], 404);
            }

            // Vérifier si le produit a des variantes avec des tailles
            $hasSizeVariants = false;

            // Vérifier d'abord si le produit a des tailles directement associées
            if ($product->sizes()->exists()) {
                $hasSizeVariants = true;
            } else {
                // Sinon, vérifier les variantes
                foreach ($product->variants as $variant) {
                    $attributs = $variant->attributs;

                    // Si attributs est une chaîne JSON, la décoder
                    if (is_string($attributs)) {
                        $attributs = json_decode($attributs, true);
                    }

                    // Vérifier si l'attribut de type 'taille' existe
                    if (is_array($attributs)) {
                        foreach ($attributs as $attribut) {
                            if (isset($attribut['type']) && $attribut['type'] === 'taille') {
                                $hasSizeVariants = true;
                                break 2; // Sortir des deux boucles
                            }
                        }
                    }
                }
            }

            // Si aucune variante avec taille n'est trouvée, retourner tous les guides disponibles
            // au lieu de renvoyer une erreur 404

            // Récupérer la catégorie du produit
            $category = $product->categorie;

            if (!$category) {
                return response()->json(['error' => 'Catégorie du produit non trouvée'], 404);
            }

            // Récupérer les guides de tailles associés à cette catégorie
            $sizeGuides = $category->sizeGuides()->where('is_active', true)->get();

            if ($sizeGuides->isEmpty()) {
                // Si aucun guide n'est associé directement, chercher dans les catégories parentes
                $parentCategory = $category->categorieParent;
                while ($parentCategory && $sizeGuides->isEmpty()) {
                    $sizeGuides = $parentCategory->sizeGuides()->where('is_active', true)->get();
                    $parentCategory = $parentCategory->categorieParent;
                }

                // Si toujours aucun guide, chercher par type de catégorie
                if ($sizeGuides->isEmpty()) {
                    // Déterminer le type de catégorie (vêtements, chaussures, etc.)
                    $categoryType = $this->determineCategoryType($category);

                    if ($categoryType) {
                        $sizeGuides = SizeGuide::where('category', $categoryType)
                            ->where('is_active', true)
                            ->get();
                    }
                }
            }

            // Si toujours aucun guide, récupérer les guides génériques
            if ($sizeGuides->isEmpty()) {
                $sizeGuides = SizeGuide::where('category', 'clothing')
                    ->orWhere('category', 'shoes')
                    ->orWhere('category', 'accessories')
                    ->where('is_active', true)
                    ->get();
            }

            return response()->json([
                'product' => [
                    'id' => $product->id,
                    'name' => $product->nom,
                    'category_id' => $category->id,
                    'category_name' => $category->getTranslatedName(),
                    'has_size_variants' => $hasSizeVariants,
                ],
                'size_guides' => $sizeGuides->map(function ($guide) {
                    return [
                        'id' => $guide->id,
                        'name' => $guide->getLocalizedNameAttribute(),
                        'category' => $guide->category,
                        'measurement_types' => $guide->measurement_types,
                        'size_systems' => $guide->size_systems,
                        'size_chart' => $guide->size_chart,
                        'instructions' => $guide->getLocalizedInstructionsAttribute(),
                        'fitting_tips' => $guide->fitting_tips,
                        'image' => $guide->image ? url('/storage/' . $guide->image) : null,
                    ];
                }),
            ]);
        } catch (\Exception $e) {
            // Log l'erreur pour le débogage
            Log::error('Erreur dans SizeGuideController::getForProduct: ' . $e->getMessage(), [
                'productId' => $productId,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Une erreur est survenue lors de la récupération des guides de tailles',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Détermine le type de catégorie (vêtements, chaussures, etc.)
     *
     * @param Categorie $category
     * @return string|null
     */
    private function determineCategoryType(Categorie $category): ?string
    {
        $name = strtolower($category->getTranslatedName());

        // Vérifier les mots-clés pour déterminer le type
        if (str_contains($name, 'vêtement') ||
            str_contains($name, 'clothing') ||
            str_contains($name, 'robe') ||
            str_contains($name, 'dress') ||
            str_contains($name, 'pantalon') ||
            str_contains($name, 'pant') ||
            str_contains($name, 'chemise') ||
            str_contains($name, 'shirt')) {
            return 'clothing';
        }

        if (str_contains($name, 'chaussure') ||
            str_contains($name, 'shoe') ||
            str_contains($name, 'basket') ||
            str_contains($name, 'sneaker') ||
            str_contains($name, 'botte') ||
            str_contains($name, 'boot')) {
            return 'shoes';
        }

        if (str_contains($name, 'accessoire') ||
            str_contains($name, 'accessory') ||
            str_contains($name, 'bijou') ||
            str_contains($name, 'jewelry') ||
            str_contains($name, 'chapeau') ||
            str_contains($name, 'hat')) {
            return 'accessories';
        }

        return null;
    }
}
