<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ReviewImageService
{
    /**
     * URL de base du projet admin_marchand_lorrelei
     */
    private string $adminBaseUrl;

    public function __construct()
    {
        $this->adminBaseUrl = rtrim(config('app.admin_base_url', 'http://localhost:8001'), '/');
    }

    /**
     * Upload des images de review vers le projet admin_marchand_lorrelei
     *
     * @param array $images Array d'UploadedFile
     * @param string $productId ID du produit
     * @return array Array contenant les informations des images uploadées
     * @throws \Exception
     */
    public function uploadReviewImages(array $images, string $productId): array
    {
        $uploadedImages = [];

        foreach ($images as $image) {
            try {
                $uploadedImage = $this->uploadSingleImage($image, $productId);
                $uploadedImages[] = $uploadedImage;
            } catch (\Exception $e) {
                Log::error('Erreur upload image review', [
                    'product_id' => $productId,
                    'error' => $e->getMessage()
                ]);
                throw $e;
            }
        }

        return $uploadedImages;
    }

    /**
     * Upload une seule image vers admin_marchand_lorrelei
     *
     * @param UploadedFile $image
     * @param string $productId
     * @return array
     * @throws \Exception
     */
    private function uploadSingleImage(UploadedFile $image, string $productId): array
    {
        // Déterminer le dossier de stockage basé sur l'ID du produit
        $productIdInt = (int)$productId;
        $folderPrefix = $productIdInt < 1000 ? '0' : substr((string)$productIdInt, 0, -3);

        // Préparer les données pour l'upload
        $response = Http::timeout(30)
            ->attach(
                'image',
                file_get_contents($image->getPathname()),
                $image->getClientOriginalName()
            )
            ->post($this->adminBaseUrl . '/api/reviews/upload-images', [
                'product_id' => $productId,
                'folder_prefix' => $folderPrefix,
            ]);

        if (!$response->successful()) {
            throw new \Exception('Erreur lors de l\'upload de l\'image: ' . $response->body());
        }

        $responseData = $response->json();

        return [
            'name' => $responseData['filename'],
            'folder' => $folderPrefix,
            'url' => $responseData['url'] ?? null
        ];
    }

    /**
     * Génère l'URL complète d'une image de review
     *
     * @param string $filename
     * @param string $folder
     * @return string
     */
    public function getImageUrl(string $filename, string $folder): string
    {
        return $this->adminBaseUrl . "/images/reviews/{$folder}/{$filename}";
    }

    /**
     * Supprime une image de review du serveur admin
     *
     * @param string $filename
     * @param string $folder
     * @return bool
     */
    public function deleteReviewImage(string $filename, string $folder): bool
    {
        try {
            $response = Http::timeout(30)
                ->delete($this->adminBaseUrl . '/api/reviews/delete-image', [
                    'filename' => $filename,
                    'folder' => $folder,
                ]);

            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Erreur suppression image review', [
                'filename' => $filename,
                'folder' => $folder,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}
