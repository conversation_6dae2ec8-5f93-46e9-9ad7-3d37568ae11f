<?php

namespace App\Providers;

use App\Helpers\ImageStorage;
use Illuminate\Support\ServiceProvider;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class ImageStorageServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Étendre la classe TemporaryUploadedFile pour ajouter notre méthode de stockage personnalisée
        TemporaryUploadedFile::macro('storeWithIdFolder', function ($id, $baseDir) {
            return ImageStorage::storeImage($this, $id, $baseDir);
        });
    }
}
