<?php

namespace App\Events;

use App\Models\ConversationMessage;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MessageSent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public ConversationMessage $message;

    /**
     * Create a new event instance.
     */
    public function __construct(ConversationMessage $message)
    {
        $this->message = $message;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('conversation.' . $this->message->conversation_id),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'message.sent';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        // Charger les relations nécessaires
        $this->message->load('conversation');
        
        // Récupérer les informations d'avatar
        $user = null;
        if ($this->message->auteur_type === 'client' && $this->message->auteur_id) {
            $client = \App\Models\Client::find($this->message->auteur_id);
            $user = $client?->user;
        } elseif ($this->message->auteur_type === 'marchand' && $this->message->auteur_id) {
            $marchand = \App\Models\Marchand::find($this->message->auteur_id);
            $user = $marchand?->user;
        }

        return [
            'message' => [
                'id' => $this->message->id,
                'conversation_id' => $this->message->conversation_id,
                'auteur_type' => $this->message->auteur_type,
                'auteur_id' => $this->message->auteur_id,
                'auteur_nom' => $this->message->auteur_nom,
                'message' => $this->message->message,
                'pieces_jointes' => $this->message->pieces_jointes ?? [],
                'created_at' => $this->message->created_at->toISOString(),
                'avatar_url' => $user?->avatar_url,
                'initials' => $user?->generateInitials() ?? strtoupper(substr($this->message->auteur_nom ?? 'U', 0, 2)),
            ],
            'conversation' => [
                'id' => $this->message->conversation->id,
                'client_id' => $this->message->conversation->client_id,
                'marchand_id' => $this->message->conversation->marchand_id,
            ],
            'timestamp' => now()->toISOString(),
        ];
    }
}
