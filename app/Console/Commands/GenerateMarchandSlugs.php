<?php

namespace App\Console\Commands;

use App\Models\Marchand;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class GenerateMarchandSlugs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'marchands:generate-slugs {--force : Force la régénération des slugs existants}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Génère les slugs pour tous les marchands qui n\'en ont pas';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Génération des slugs pour les marchands...');

        $force = $this->option('force');

        // Récupérer les marchands sans slug ou tous si --force
        $query = Marchand::query();

        if (!$force) {
            $query->whereNull('slug')->orWhere('slug', '');
        }

        $marchands = $query->get();

        if ($marchands->isEmpty()) {
            $this->info('✅ Aucun marchand à traiter.');
            return Command::SUCCESS;
        }

        $this->info("📊 {$marchands->count()} marchand(s) à traiter...");

        $progressBar = $this->output->createProgressBar($marchands->count());
        $progressBar->start();

        $updated = 0;
        $errors = 0;

        foreach ($marchands as $marchand) {
            try {
                $oldSlug = $marchand->slug;

                // Générer un nouveau slug
                $newSlug = $this->generateUniqueSlug($marchand->nomEntreprise, $marchand->id);

                // Mettre à jour le marchand
                $marchand->update(['slug' => $newSlug]);

                $updated++;

                if ($this->output->isVerbose()) {
                    $this->newLine();
                    $this->line("✅ {$marchand->nomEntreprise}: '{$oldSlug}' → '{$newSlug}'");
                }

            } catch (\Exception $e) {
                $errors++;

                if ($this->output->isVerbose()) {
                    $this->newLine();
                    $this->error("❌ Erreur pour {$marchand->nomEntreprise}: {$e->getMessage()}");
                }
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        // Résumé
        $this->info("🎉 Génération terminée !");
        $this->table(
            ['Statut', 'Nombre'],
            [
                ['✅ Mis à jour', $updated],
                ['❌ Erreurs', $errors],
                ['📊 Total traité', $marchands->count()],
            ]
        );

        if ($errors > 0) {
            $this->warn("⚠️  {$errors} erreur(s) détectée(s). Utilisez -v pour plus de détails.");
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }

    /**
     * Génère un slug unique pour un marchand
     */
    private function generateUniqueSlug(string $nomEntreprise, int $excludeId = null): string
    {
        $baseSlug = Str::slug($nomEntreprise);
        $slug = $baseSlug;
        $counter = 1;

        // Vérifier l'unicité du slug
        while (Marchand::where('slug', $slug)->when($excludeId, function($query, $excludeId) {
            return $query->where('id', '!=', $excludeId);
        })->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }
}
