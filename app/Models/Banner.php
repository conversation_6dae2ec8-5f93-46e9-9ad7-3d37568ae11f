<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class Banner extends Model
{
    use HasFactory, HasUuids, HasTranslations;

    /**
     * Les attributs qui sont traduisibles.
     *
     * @var array
     */
    public $translatable = ['title', 'description', 'button_text'];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'image_url',
        'target_url',
        'position',
        'title',
        'description',
        'button_text',
        'type',
        'start_date',
        'end_date',
        'is_active',
        'priorite',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = ['full_image_url', 'thumbnail_urls'];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'start_date' => 'datetime',
            'end_date' => 'datetime',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the full URL for the banner image.
     *
     * @return string|null
     */
    public function getFullImageUrlAttribute(): ?string
    {
        if (empty($this->image_url)) {
            return null;
        }
        if (preg_match('/^https?:\/\//', $this->image_url)) {
            // Si l'URL est déjà complète, retourner telle quelle
            return $this->image_url;
        }
        // Vérifier si l'image est déjà un chemin complet
        if (strpos($this->image_url, '/') !== false) {
            return url('/images/' . $this->image_url);
        }

        // Déterminer le dossier basé sur l'ID de la bannière
        // Comme Banner utilise des UUIDs, nous utilisons les 2 premiers caractères
        $folderPrefix = substr($this->id, 0, 2);
        return url("/images/banners/{$folderPrefix}/{$this->image_url}");
    }

    /**
     * Get the full URL for the banner image.
     *
     * @return string|null
     */
    public function getImageUrlAttribute(): ?string
    {
        if (empty($this->attributes['image_url'])) {
            return null;
        }

        return \App\Helpers\ImageUrlHelper::getImageUrl($this->attributes['image_url'], 'banners');
    }

    /**
     * Get the thumbnail URLs for the banner image.
     *
     * @return array
     */
    public function getThumbnailUrlsAttribute(): array
    {
        if (empty($this->attributes['image_url'])) {
            return [
                'small' => null,
                'medium' => null,
                'large' => null
            ];
        }
        // Vérifier si l'URL commence par "http" ou "https"
        if (preg_match('/^https?:\/\//', $this->image_url)) {
            // Si l'URL est déjà complète, utiliser telle quelle
            $imagePath = $this->image_url;
        } elseif (strpos($this->image_url, '/') !== false) {
            // Si l'image est déjà un chemin complet, utiliser tel quel
            $imagePath = 'banners/' . $this->image_url;
        }

        return [
            'small' => \App\Helpers\ImageUrlHelper::getThumbnailUrl($this->attributes['image_url'], 'small', 'banners'),
            'medium' => \App\Helpers\ImageUrlHelper::getThumbnailUrl($this->attributes['image_url'], 'medium', 'banners'),
            'large' => \App\Helpers\ImageUrlHelper::getThumbnailUrl($this->attributes['image_url'], 'large', 'banners')
        ];
    }
}
