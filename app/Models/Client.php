<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Client extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'prenom',
        'nom',
        'adresse_id',
        'telephone',
        'dateDeNaissance',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'dateDeNaissance' => 'date',
        ];
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function adresse(): BelongsTo
    {
        return $this->belongsTo(Adresse::class);
    }

    /**
     * Relation avec les adresses de l'utilisateur
     * Un client peut avoir plusieurs adresses via son user_id
     */
    public function adresses(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Adresse::class, 'user_id', 'user_id');
    }

    public function commandes(): Has<PERSON>any
    {
        return $this->hasMany(Commande::class);
    }
}
