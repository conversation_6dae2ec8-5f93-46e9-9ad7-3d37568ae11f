<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ArticleCommande extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'commande_id',
        'produit_id',
        'quantite',
        'prixUnitaire',
        // Nouveaux champs pour le système multi-marchands
        'sous_commande_id',
        'prix_unitaire_ht',
        'prix_unitaire_ttc',
        'taux_tva',
        'montant_tva',
        'taux_commission',
        'montant_commission',
        'montant_total_ht',
        'montant_total_ttc',
        'nom_produit_commande',
        'description_produit_commande',
        'poids_unitaire',
        'poids_total',
        'statut_article',
        'eligible_remboursement',
        'montant_rembourse',
        'variantes_produit',
        'montant_reduction',
        'code_promotion',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'prixUnitaire' => 'decimal:2',
            // Nouveaux casts
            'prix_unitaire_ht' => 'decimal:2',
            'prix_unitaire_ttc' => 'decimal:2',
            'taux_tva' => 'decimal:2',
            'montant_tva' => 'decimal:2',
            'taux_commission' => 'decimal:2',
            'montant_commission' => 'decimal:2',
            'montant_total_ht' => 'decimal:2',
            'montant_total_ttc' => 'decimal:2',
            'poids_unitaire' => 'decimal:2',
            'poids_total' => 'decimal:2',
            'montant_rembourse' => 'decimal:2',
            'montant_reduction' => 'decimal:2',
            'eligible_remboursement' => 'boolean',
            'variantes_produit' => 'array',
        ];
    }

    public function commande(): BelongsTo
    {
        return $this->belongsTo(Commande::class);
    }

    public function produit(): BelongsTo
    {
        return $this->belongsTo(Produit::class);
    }

    /**
     * Nouvelles relations pour le système multi-marchands
     */

    /**
     * Relation avec la sous-commande vendeur
     */
    public function sousCommande(): BelongsTo
    {
        return $this->belongsTo(SousCommandeVendeur::class, 'sous_commande_id');
    }

    /**
     * Scopes pour les requêtes courantes
     */
    public function scopeParStatut($query, $statut)
    {
        return $query->where('statut_article', $statut);
    }

    public function scopeEligibleRemboursement($query)
    {
        return $query->where('eligible_remboursement', true);
    }

    /**
     * Méthodes utilitaires
     */

    /**
     * Calcule le montant total HT
     */
    public function calculerMontantTotalHT(): void
    {
        $this->montant_total_ht = $this->prix_unitaire_ht * $this->quantite;
        $this->save();
    }

    /**
     * Calcule le montant total TTC
     */
    public function calculerMontantTotalTTC(): void
    {
        $this->montant_total_ttc = $this->prix_unitaire_ttc * $this->quantite;
        $this->save();
    }

    /**
     * Vérifie si l'article peut être remboursé
     */
    public function peutÊtreRembourse(): bool
    {
        return $this->eligible_remboursement &&
               in_array($this->statut_article, ['livre', 'expedie']) &&
               $this->montant_rembourse < $this->montant_total_ttc;
    }
}
