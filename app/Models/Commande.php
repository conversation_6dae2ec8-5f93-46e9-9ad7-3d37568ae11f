<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Commande extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'client_id',
        'marchand_id',
        'montantTotal',
        'statut',
        'adresse_livraison_id',
        'creeLe',
        'dateExpeditionPrevue',
        'dateLivraisonPrevue',
        'codeSuivi',
        'adresse_id',
        // Nouveaux champs pour le système multi-marchands
        'commande_principale_id',
        'est_sous_commande',
        'type_commande',
        'taux_commission_applique',
        'montant_commission_calcule',
        'metadata_migration',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'montantTotal' => 'decimal:2',
            'creeLe' => 'timestamp',
            'dateExpeditionPrevue' => 'date',
            'dateLivraisonPrevue' => 'date',
            'adresse_id' => 'integer',
            // Nouveaux casts
            'taux_commission_applique' => 'decimal:2',
            'montant_commission_calcule' => 'decimal:2',
            'est_sous_commande' => 'boolean',
            'metadata_migration' => 'array',
        ];
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function marchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class);
    }

    public function adresse(): BelongsTo
    {
        return $this->belongsTo(Adresse::class);
    }

    public function adresseLivraison(): BelongsTo
    {
        return $this->belongsTo(Adresse::class, 'adresse_livraison_id');
    }

    public function articleCommandes(): HasMany
    {
        return $this->hasMany(ArticleCommande::class);
    }

    /**
     * Nouvelles relations pour le système multi-marchands
     */

    /**
     * Relation avec la commande principale (pour les commandes legacy)
     */
    public function commandePrincipale(): BelongsTo
    {
        return $this->belongsTo(CommandePrincipale::class);
    }

    /**
     * Scopes pour les requêtes courantes
     */
    public function scopeLegacy($query)
    {
        return $query->where('type_commande', 'legacy');
    }

    public function scopeSousCommande($query)
    {
        return $query->where('est_sous_commande', true);
    }
}
