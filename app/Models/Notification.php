<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Support\Carbon;

class Notification extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'type',
        'notifiable_type',
        'notifiable_id',
        'data',
        'read_at',
        'related_order_id',
        'related_suborder_id',
        'priority',
        'channel',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'data' => 'array',
        'read_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Types de notifications disponibles
     */
    public const TYPES = [
        // Notifications Client
        'order_created' => 'Commande créée avec succès',
        'payment_confirmed' => 'Paiement confirmé',
        'order_processing' => 'Commande en cours de traitement',
        'order_shipped' => 'Commande expédiée',
        'order_delivered' => 'Commande livrée',
        'order_cancelled' => 'Commande annulée',
        'refund_processed' => 'Remboursement traité',
        'support_ticket_created' => 'Ticket de support créé',
        'support_ticket_replied' => 'Réponse au ticket de support',

        // Notifications Marchand
        'new_order_received' => 'Nouvelle commande reçue',
        'payment_received' => 'Paiement reçu pour une commande',
        'payout_processed' => 'Versement effectué',
        'product_low_stock' => 'Stock faible sur un produit',
        'product_out_of_stock' => 'Produit en rupture de stock',
        'review_received' => 'Nouvel avis client reçu',
        'support_ticket_assigned' => 'Ticket de support assigné',

        // Notifications Admin
        'new_merchant_registration' => 'Nouveau marchand inscrit',
        'high_value_order' => 'Commande de valeur élevée',
        'payment_failed' => 'Échec de paiement',
        'dispute_created' => 'Nouveau litige créé',
        'system_alert' => 'Alerte système',
    ];

    /**
     * Priorités disponibles
     */
    public const PRIORITIES = [
        'low' => 'Faible',
        'medium' => 'Moyenne',
        'high' => 'Haute',
        'urgent' => 'Urgente',
    ];

    /**
     * Canaux disponibles
     */
    public const CHANNELS = [
        'database' => 'Base de données',
        'email' => 'Email',
        'sms' => 'SMS',
        'push' => 'Push',
    ];

    /**
     * Statuts disponibles
     */
    public const STATUSES = [
        'pending' => 'En attente',
        'sent' => 'Envoyé',
        'failed' => 'Échec',
        'read' => 'Lu',
    ];

    /**
     * Relation polymorphe avec l'entité notifiable
     */
    public function notifiable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Relation avec la commande principale
     */
    public function relatedOrder(): BelongsTo
    {
        return $this->belongsTo(CommandePrincipale::class, 'related_order_id');
    }

    /**
     * Relation avec la sous-commande
     */
    public function relatedSuborder(): BelongsTo
    {
        return $this->belongsTo(SousCommandeVendeur::class, 'related_suborder_id');
    }

    /**
     * Scope pour les notifications non lues
     */
    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    /**
     * Scope pour les notifications lues
     */
    public function scopeRead($query)
    {
        return $query->whereNotNull('read_at');
    }

    /**
     * Scope pour filtrer par type
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope pour filtrer par priorité
     */
    public function scopeWithPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope pour filtrer par canal
     */
    public function scopeForChannel($query, string $channel)
    {
        return $query->where('channel', $channel);
    }

    /**
     * Scope pour les notifications récentes
     */
    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('created_at', '>=', Carbon::now()->subDays($days));
    }

    /**
     * Marque la notification comme lue
     */
    public function markAsRead(): void
    {
        if ($this->read_at === null) {
            $this->update([
                'read_at' => Carbon::now(),
                'status' => 'read'
            ]);
        }
    }

    /**
     * Marque la notification comme non lue
     */
    public function markAsUnread(): void
    {
        $this->update([
            'read_at' => null,
            'status' => 'pending'
        ]);
    }

    /**
     * Vérifie si la notification est lue
     */
    public function isRead(): bool
    {
        return $this->read_at !== null;
    }

    /**
     * Vérifie si la notification est urgente
     */
    public function isUrgent(): bool
    {
        return $this->priority === 'urgent';
    }

    /**
     * Obtient le titre de la notification
     */
    public function getTitle(): string
    {
        return $this->data['title'] ?? self::TYPES[$this->type] ?? 'Notification';
    }

    /**
     * Obtient le message de la notification
     */
    public function getMessage(): string
    {
        return $this->data['message'] ?? '';
    }

    /**
     * Obtient l'URL d'action de la notification
     */
    public function getActionUrl(): ?string
    {
        return $this->data['action_url'] ?? null;
    }

    /**
     * Obtient l'icône de la notification
     */
    public function getIcon(): string
    {
        return $this->data['icon'] ?? match($this->type) {
            'order_created', 'new_order_received' => '🛍️',
            'payment_confirmed', 'payment_received' => '💳',
            'order_shipped' => '📦',
            'order_delivered' => '✅',
            'order_cancelled' => '❌',
            'refund_processed' => '💰',
            'product_low_stock', 'product_out_of_stock' => '⚠️',
            'payout_processed' => '💸',
            'review_received' => '⭐',
            'support_ticket_created', 'support_ticket_assigned' => '🎫',
            'system_alert' => '🚨',
            default => '🔔',
        };
    }

    /**
     * Obtient la couleur de la notification selon la priorité
     */
    public function getColor(): string
    {
        return match($this->priority) {
            'urgent' => 'red',
            'high' => 'orange',
            'medium' => 'blue',
            'low' => 'gray',
            default => 'blue',
        };
    }

    /**
     * Formate la notification pour l'affichage
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'title' => $this->getTitle(),
            'message' => $this->getMessage(),
            'action_url' => $this->getActionUrl(),
            'icon' => $this->getIcon(),
            'color' => $this->getColor(),
            'is_read' => $this->isRead(),
            'is_urgent' => $this->isUrgent(),
            'time_ago' => $this->created_at->diffForHumans(),
        ]);
    }
}
