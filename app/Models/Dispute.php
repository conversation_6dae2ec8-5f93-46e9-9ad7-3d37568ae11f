<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class Dispute extends Model
{
    use HasFactory, HasUuids;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'disputes';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'commande_principale_id',
        'sous_commande_id',
        'client_id',
        'marchand_id',
        'escrow_transaction_id',
        'numero_litige',
        'type_litige',
        'statut',
        'priorite',
        'sujet',
        'description',
        'solution_souhaitee',
        'montant_conteste',
        'montant_rembourse',
        'montant_compensation',
        'date_ouverture',
        'date_premiere_reponse',
        'date_resolution',
        'date_fermeture',
        'date_limite_reponse',
        'assigne_a',
        'resolu_par',
        'resolution_details',
        'notes_internes',
        'satisfaction_client',
        'commentaire_satisfaction',
        'pieces_jointes',
        'historique_statuts',
        'metadata',
        'urgent',
        'escalade_automatique',
        'notification_client',
        'notification_marchand',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'montant_conteste' => 'decimal:2',
        'montant_rembourse' => 'decimal:2',
        'montant_compensation' => 'decimal:2',
        'date_ouverture' => 'datetime',
        'date_premiere_reponse' => 'datetime',
        'date_resolution' => 'datetime',
        'date_fermeture' => 'datetime',
        'date_limite_reponse' => 'datetime',
        'satisfaction_client' => 'integer',
        'pieces_jointes' => 'array',
        'historique_statuts' => 'array',
        'metadata' => 'array',
        'urgent' => 'boolean',
        'escalade_automatique' => 'boolean',
        'notification_client' => 'boolean',
        'notification_marchand' => 'boolean',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'statut' => 'ouvert',
        'priorite' => 'normale',
        'montant_rembourse' => 0,
        'montant_compensation' => 0,
        'urgent' => false,
        'escalade_automatique' => true,
        'notification_client' => true,
        'notification_marchand' => true,
    ];

    /**
     * Relation avec la commande principale
     */
    public function commandePrincipale(): BelongsTo
    {
        return $this->belongsTo(CommandePrincipale::class);
    }

    /**
     * Relation avec la sous-commande
     */
    public function sousCommande(): BelongsTo
    {
        return $this->belongsTo(SousCommandeVendeur::class, 'sous_commande_id');
    }

    /**
     * Relation avec le client
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Relation avec le marchand
     */
    public function marchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class);
    }

    /**
     * Relation avec la transaction escrow
     */
    public function escrowTransaction(): BelongsTo
    {
        return $this->belongsTo(EscrowTransaction::class);
    }

    /**
     * Relation avec l'utilisateur assigné
     */
    public function assigneA(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigne_a');
    }

    /**
     * Relation avec l'utilisateur qui a résolu
     */
    public function resoluPar(): BelongsTo
    {
        return $this->belongsTo(User::class, 'resolu_par');
    }

    /**
     * Relation avec les messages du litige
     */
    public function messages(): HasMany
    {
        return $this->hasMany(DisputeMessage::class);
    }

    /**
     * Génère un numéro de litige unique
     */
    public static function generateNumeroLitige(): string
    {
        $prefix = 'LIT';
        $timestamp = now()->format('ymd');

        // Compter les litiges du jour
        $count = static::whereDate('created_at', today())->count() + 1;
        $sequence = str_pad($count, 4, '0', STR_PAD_LEFT);

        return $prefix . '-' . $timestamp . '-' . $sequence;
    }

    /**
     * Calcule la date limite de réponse selon la priorité
     */
    public function calculateDateLimiteReponse(): \DateTime
    {
        $heures = match($this->priorite) {
            'urgente' => 4,    // 4 heures
            'haute' => 24,     // 1 jour
            'normale' => 72,   // 3 jours
            'basse' => 168,    // 7 jours
            default => 72
        };

        return $this->date_ouverture->addHours($heures);
    }

    /**
     * Met à jour la date limite de réponse
     */
    public function updateDateLimiteReponse(): void
    {
        $this->update([
            'date_limite_reponse' => $this->calculateDateLimiteReponse()
        ]);
    }

    /**
     * Vérifie si le litige est en retard
     */
    public function isEnRetard(): bool
    {
        if (!$this->date_limite_reponse) {
            return false;
        }

        return now() > $this->date_limite_reponse &&
               !in_array($this->statut, ['resolu', 'ferme_admin', 'ferme_client']);
    }

    /**
     * Vérifie si le litige peut être escaladé
     */
    public function peutEtreEscalade(): bool
    {
        return $this->escalade_automatique &&
               $this->isEnRetard() &&
               $this->statut === 'en_cours';
    }

    /**
     * Change le statut du litige avec historique
     */
    public function changerStatut(string $nouveauStatut, ?int $userId = null, ?string $commentaire = null): void
    {
        $ancienStatut = $this->statut;

        // Mettre à jour l'historique
        $historique = $this->historique_statuts ?? [];
        $historique[] = [
            'ancien_statut' => $ancienStatut,
            'nouveau_statut' => $nouveauStatut,
            'date' => now()->toISOString(),
            'user_id' => $userId,
            'commentaire' => $commentaire
        ];

        // Mettre à jour les dates selon le statut
        $updates = [
            'statut' => $nouveauStatut,
            'historique_statuts' => $historique
        ];

        if ($nouveauStatut === 'en_cours' && !$this->date_premiere_reponse) {
            $updates['date_premiere_reponse'] = now();
        }

        if (in_array($nouveauStatut, ['resolu', 'ferme_admin', 'ferme_client'])) {
            $updates['date_resolution'] = now();
            if (!$this->date_fermeture) {
                $updates['date_fermeture'] = now();
            }
            if ($userId) {
                $updates['resolu_par'] = $userId;
            }
        }

        $this->update($updates);
    }

    /**
     * Assigne le litige à un utilisateur
     */
    public function assignerA(int $userId, ?string $commentaire = null): void
    {
        $this->update(['assigne_a' => $userId]);

        if ($this->statut === 'ouvert') {
            $this->changerStatut('en_cours', $userId, $commentaire);
        }
    }

    /**
     * Obtient le statut formaté pour l'affichage
     */
    public function getStatutFormateAttribute(): string
    {
        return match($this->statut) {
            'ouvert' => 'Ouvert',
            'en_cours' => 'En cours',
            'attente_client' => 'Attente client',
            'attente_marchand' => 'Attente marchand',
            'resolu' => 'Résolu',
            'ferme_admin' => 'Fermé par admin',
            'ferme_client' => 'Fermé par client',
            'escalade' => 'Escaladé',
            'expire' => 'Expiré',
            default => 'Inconnu'
        };
    }

    /**
     * Obtient la couleur du badge selon le statut
     */
    public function getStatutColorAttribute(): string
    {
        return match($this->statut) {
            'ouvert' => 'warning',
            'en_cours' => 'info',
            'attente_client', 'attente_marchand' => 'secondary',
            'resolu' => 'success',
            'ferme_admin', 'ferme_client' => 'dark',
            'escalade' => 'danger',
            'expire' => 'muted',
            default => 'light'
        };
    }

    /**
     * Obtient la priorité formatée
     */
    public function getPrioriteFormateAttribute(): string
    {
        return match($this->priorite) {
            'basse' => 'Basse',
            'normale' => 'Normale',
            'haute' => 'Haute',
            'urgente' => 'Urgente',
            default => 'Normale'
        };
    }

    /**
     * Obtient la couleur de la priorité
     */
    public function getPrioriteColorAttribute(): string
    {
        return match($this->priorite) {
            'basse' => 'secondary',
            'normale' => 'primary',
            'haute' => 'warning',
            'urgente' => 'danger',
            default => 'primary'
        };
    }

    /**
     * Scope pour les litiges ouverts
     */
    public function scopeOuverts($query)
    {
        return $query->whereIn('statut', ['ouvert', 'en_cours', 'attente_client', 'attente_marchand']);
    }

    /**
     * Scope pour les litiges en retard
     */
    public function scopeEnRetard($query)
    {
        return $query->where('date_limite_reponse', '<', now())
                    ->whereNotIn('statut', ['resolu', 'ferme_admin', 'ferme_client']);
    }

    /**
     * Scope pour les litiges urgents
     */
    public function scopeUrgents($query)
    {
        return $query->where(function($q) {
            $q->where('urgent', true)
              ->orWhere('priorite', 'urgente');
        });
    }

    /**
     * Scope pour les litiges assignés à un utilisateur
     */
    public function scopeAssignesA($query, int $userId)
    {
        return $query->where('assigne_a', $userId);
    }

    /**
     * Accesseur pour le type de litige formaté
     */
    public function getTypeLitigeFormateAttribute(): string
    {
        $types = [
            'non_livraison' => 'Commande non livrée',
            'produit_defectueux' => 'Produit défectueux ou endommagé',
            'produit_different' => 'Produit différent de la description',
            'livraison_partielle' => 'Livraison incomplète',
            'retard_livraison' => 'Retard de livraison',
            'frais_supplementaires' => 'Frais supplémentaires non prévus',
            'service_client' => 'Problème de service client',
            'autre' => 'Autre motif'
        ];

        return $types[$this->type_litige] ?? $this->type_litige;
    }

    /**
     * Accesseur pour le montant formaté
     */
    public function getMontantFormateAttribute(): string
    {
        if (!$this->montant_conteste) {
            return '';
        }
        return number_format($this->montant_conteste, 0, ',', ' ') . ' FCFA';
    }
}
