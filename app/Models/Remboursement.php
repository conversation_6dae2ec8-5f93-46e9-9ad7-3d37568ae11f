<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Remboursement extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'remboursements';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'commande_principale_id',
        'sous_commande_id',
        'client_id',
        'marchand_id',
        'reference_remboursement',
        'type_remboursement',
        'motif',
        'montant_demande',
        'montant_approuve',
        'montant_rembourse',
        'frais_retour',
        'devise',
        'statut',
        'retour_physique_requis',
        'transporteur_retour',
        'numero_suivi_retour',
        'date_retour_expedie',
        'date_retour_recu',
        'description_probleme',
        'justification_client',
        'evaluation_marchand',
        'decision_admin',
        'motif_refus',
        'preuves_client',
        'preuves_marchand',
        'date_demande',
        'date_evaluation',
        'date_decision',
        'date_remboursement',
        'delai_retour_jours',
        'traite_par_marchand',
        'traite_par_admin',
        'reference_transaction_remboursement',
        'methode_remboursement',
        'en_litige',
        'date_ouverture_litige',
        'details_litige',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'montant_demande' => 'decimal:2',
        'montant_approuve' => 'decimal:2',
        'montant_rembourse' => 'decimal:2',
        'frais_retour' => 'decimal:2',
        'retour_physique_requis' => 'boolean',
        'en_litige' => 'boolean',
        'date_retour_expedie' => 'datetime',
        'date_retour_recu' => 'datetime',
        'date_demande' => 'datetime',
        'date_evaluation' => 'datetime',
        'date_decision' => 'datetime',
        'date_remboursement' => 'datetime',
        'date_ouverture_litige' => 'datetime',
        'delai_retour_jours' => 'integer',
        'preuves_client' => 'array',
        'preuves_marchand' => 'array',
        'metadata' => 'array',
    ];

    /**
     * Relation avec la commande principale
     */
    public function commandePrincipale(): BelongsTo
    {
        return $this->belongsTo(CommandePrincipale::class);
    }

    /**
     * Relation avec la sous-commande
     */
    public function sousCommande(): BelongsTo
    {
        return $this->belongsTo(SousCommandeVendeur::class, 'sous_commande_id');
    }

    /**
     * Relation avec le client
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Relation avec le marchand
     */
    public function marchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class);
    }

    /**
     * Relation avec l'utilisateur qui a traité côté marchand
     */
    public function traiteurMarchand(): BelongsTo
    {
        return $this->belongsTo(User::class, 'traite_par_marchand');
    }

    /**
     * Relation avec l'administrateur qui a traité
     */
    public function traiteurAdmin(): BelongsTo
    {
        return $this->belongsTo(User::class, 'traite_par_admin');
    }

    /**
     * Scopes pour les requêtes courantes
     */
    public function scopePourClient($query, $clientId)
    {
        return $query->where('client_id', $clientId);
    }

    public function scopePourMarchand($query, $marchandId)
    {
        return $query->where('marchand_id', $marchandId);
    }

    public function scopeEnAttente($query)
    {
        return $query->where('statut', 'demande');
    }

    public function scopeEnLitige($query)
    {
        return $query->where('en_litige', true);
    }

    public function scopeComplete($query)
    {
        return $query->where('statut', 'complete');
    }

    /**
     * Méthodes utilitaires
     */
    
    /**
     * Vérifie si le remboursement peut être approuvé
     */
    public function peutÊtreApprouvé(): bool
    {
        return in_array($this->statut, ['demande', 'en_evaluation', 'retour_recu']);
    }

    /**
     * Vérifie si le remboursement peut être refusé
     */
    public function peutÊtreRefusé(): bool
    {
        return in_array($this->statut, ['demande', 'en_evaluation', 'retour_recu']);
    }

    /**
     * Vérifie si le délai de retour est dépassé
     */
    public function delaiRetourDépasse(): bool
    {
        if (!$this->retour_physique_requis || $this->date_retour_recu) {
            return false;
        }

        $dateLimit = $this->date_demande->addDays($this->delai_retour_jours);
        return now()->isAfter($dateLimit);
    }

    /**
     * Génère une référence unique pour le remboursement
     */
    public static function genererReferenceRemboursement(): string
    {
        $prefix = 'RMB';
        $date = now()->format('Ymd');
        $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        $reference = $prefix . $date . $random;
        
        // Vérifier l'unicité
        while (self::where('reference_remboursement', $reference)->exists()) {
            $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
            $reference = $prefix . $date . $random;
        }
        
        return $reference;
    }

    /**
     * Approuve le remboursement
     */
    public function approuver(float $montantApprouve, ?int $traiteurId = null, ?string $notes = null): void
    {
        $this->statut = 'approuve';
        $this->montant_approuve = $montantApprouve;
        $this->date_decision = now();
        $this->traite_par_admin = $traiteurId;
        
        if ($notes) {
            $this->decision_admin = $notes;
        }

        $this->save();

        // Déclencher le processus de remboursement
        // event(new RemboursementApprouve($this));
    }

    /**
     * Refuse le remboursement
     */
    public function refuser(string $motifRefus, ?int $traiteurId = null): void
    {
        $this->statut = 'refuse';
        $this->motif_refus = $motifRefus;
        $this->date_decision = now();
        $this->traite_par_admin = $traiteurId;

        $this->save();

        // Notifier le client
        // event(new RemboursementRefuse($this));
    }

    /**
     * Marque le remboursement comme terminé
     */
    public function marquerComplete(string $referenceTransaction, ?string $methode = null): void
    {
        $this->statut = 'complete';
        $this->montant_rembourse = $this->montant_approuve;
        $this->date_remboursement = now();
        $this->reference_transaction_remboursement = $referenceTransaction;
        $this->methode_remboursement = $methode;

        $this->save();

        // Notifier le client
        // event(new RemboursementComplete($this));
    }

    /**
     * Ouvre un litige
     */
    public function ouvrirLitige(string $details): void
    {
        $this->en_litige = true;
        $this->statut = 'litige';
        $this->date_ouverture_litige = now();
        $this->details_litige = $details;

        $this->save();

        // Notifier les administrateurs
        // event(new LitigeOuvert($this));
    }
}
